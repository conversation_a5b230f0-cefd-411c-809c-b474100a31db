#!/bin/bash

# خطوات النشر على الاستضافة - تشغيل هذه الأوامر بالترتيب

echo "🚀 بدء عملية النشر..."

# 1. تحديث Composer
echo "📦 تحديث Composer..."
composer install --optimize-autoloader --no-dev

# 2. مسح Cache
echo "🧹 مسح Cache..."
php artisan config:clear
php artisan cache:clear
php artisan view:clear
php artisan route:clear

# 3. إنشاء Storage Link
echo "🔗 إنشاء Storage Link..."
php artisan storage:link

# 4. التحقق من الصور
echo "🖼️ التحقق من الصور..."
php artisan storage:check-images

# 5. إنشاء Cache جديد
echo "⚡ إنشاء Cache جديد..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 6. تحديث أذونات المجلدات
echo "🔐 تحديث الأذونات..."
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
chmod -R 755 public/storage/

echo "✅ تم الانتهاء من النشر!"
echo ""
echo "📝 ملاحظات مهمة:"
echo "1. تأكد من تحديث APP_URL في ملف .env"
echo "2. تأكد من رفع مجلد storage/app/public مع الصور"
echo "3. في حالة عدم ظهور الصور، تحقق من logs: tail -f storage/logs/laravel.log"
