<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;

class ImageHelper
{
    /**
     * Get image URL with fallback
     */
    public static function getImageUrl($imagePath, $fallback = null)
    {
        if (!$imagePath) {
            return $fallback ?: asset('admin/assets/img/product/noimage.png');
        }

        // Check if image exists in storage
        if (Storage::disk('public')->exists(str_replace('public/', '', $imagePath))) {
            return Storage::url($imagePath);
        }

        // Fallback to asset if storage fails
        return $fallback ?: asset('admin/assets/img/product/noimage.png');
    }

    /**
     * Get product image URL
     */
    public static function getProductImageUrl($product)
    {
        $image = $product->images->first();
        
        if ($image && $image->image_path) {
            return self::getImageUrl($image->image_path);
        }

        return asset('admin/assets/img/product/noimage.png');
    }

    /**
     * Check if storage link exists and create if needed
     */
    public static function ensureStorageLink()
    {
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        if (!File::exists($linkPath)) {
            if (function_exists('symlink')) {
                symlink($targetPath, $linkPath);
                return true;
            }
        }

        return File::exists($linkPath);
    }
}
