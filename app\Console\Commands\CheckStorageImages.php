<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use App\Models\Product;

class CheckStorageImages extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'storage:check-images';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check storage images and create symbolic link if needed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking storage configuration...');

        // Check if storage link exists
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        if (!File::exists($linkPath)) {
            $this->warn('Storage link does not exist. Creating...');
            
            if (function_exists('symlink')) {
                symlink($targetPath, $linkPath);
                $this->info('Storage link created successfully.');
            } else {
                $this->error('Cannot create symbolic link. Please create it manually.');
                $this->line('Run: ln -s ' . $targetPath . ' ' . $linkPath);
            }
        } else {
            $this->info('Storage link exists.');
        }

        // Check storage directories
        $directories = ['products', 'carousels', 'catalogs', 'side_banners'];
        
        foreach ($directories as $dir) {
            $path = storage_path('app/public/' . $dir);
            if (!File::exists($path)) {
                File::makeDirectory($path, 0755, true);
                $this->info("Created directory: {$dir}");
            } else {
                $this->info("Directory exists: {$dir}");
            }
        }

        // Check product images
        $this->info('Checking product images...');
        $products = Product::with('images')->get();
        $missingImages = 0;

        foreach ($products as $product) {
            foreach ($product->images as $image) {
                $imagePath = str_replace('public/', '', $image->image_path);
                if (!Storage::disk('public')->exists($imagePath)) {
                    $missingImages++;
                    $this->warn("Missing image: {$image->image_path} for product: {$product->name}");
                }
            }
        }

        if ($missingImages > 0) {
            $this->error("Found {$missingImages} missing images.");
        } else {
            $this->info('All product images exist.');
        }

        $this->info('Storage check completed.');
    }
}
